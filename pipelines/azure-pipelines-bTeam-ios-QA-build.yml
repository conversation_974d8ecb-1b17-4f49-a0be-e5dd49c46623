trigger: none

resources:
  repositories:
    - repository: bMobiles
      type: git
      name: Mobile apps/bMobiles
      # trigger:
      #   branches:
      #     include:
      #     - qa

variables:
  certFile: CertificatesBSuiteProd.p12
  provisioningProfileFile: 'new_bTeam_provision.mobileprovision'
  # Project-specific build destination settings with optimizations
  iOSDestinations: '-destination "generic/platform=iOS,name=iPhone" -verbose TARGETED_DEVICE_FAMILY="1" SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD="NO" -parallelizeTargets -jobs $(nproc)'

jobs:
- job: iOS_build
  timeoutInMinutes: 60  # Reduced from 120 with optimizations
  pool:
    vmImage: 'macOS-15'  # Latest macOS for better performance

  variables:
    Project: 'Mobile apps'
    Repo: bMobiles
    Branch: master
    platformPrefix: 'packages/engine'
    Configuration: Release
    Workspace: bTeam
    Scheme: bTeam
    Sdk: iphoneos
    OutDir: '$(Repo)/$(platformPrefix)/ios/build/Build/Products/'
    provisioningProfileName: 'new bTeam provision'
    signingIdentity: 'Apple Distribution: BENEFIT SOFTWARE (FTRP3S9CH8)'
    legacyPeerDeps: true
    Xcode: 16.2
    # Cache keys for dependencies
    npm.cacheKey: 'npm | "$(Agent.OS)" | $(Repo)/package-lock.json'
    pods.cacheKey: 'pods | "$(Agent.OS)" | $(Repo)/$(platformPrefix)/ios/Podfile.lock'
    derivedData.cacheKey: 'deriveddata | "$(Agent.OS)" | $(Repo)/$(platformPrefix)/ios/$(Workspace).xcworkspace/**'

  steps:
  - checkout: self
  - checkout: git://${{ variables['Project'] }}/${{ variables['Repo'] }}@${{ variables['Branch'] }}

  # Cache npm dependencies
  - task: Cache@2
    displayName: Cache npm dependencies
    inputs:
      key: $(npm.cacheKey)
      path: $(Repo)/node_modules
      cacheHitVar: 'npm.cache.restored'

  # Cache CocoaPods
  - task: Cache@2
    displayName: Cache CocoaPods
    inputs:
      key: $(pods.cacheKey)
      path: $(Repo)/$(platformPrefix)/ios/Pods
      cacheHitVar: 'pods.cache.restored'

  # Cache Xcode DerivedData for faster builds
  - task: Cache@2
    displayName: Cache Xcode DerivedData
    inputs:
      key: $(derivedData.cacheKey)
      path: ~/Library/Developer/Xcode/DerivedData
      cacheHitVar: 'derivedData.cache.restored'

  - task: Bash@3
    displayName: Setup project configuration
    inputs:
      targetType: 'inline'
      script: |
        # Install jq if not cached
        if ! command -v jq &> /dev/null; then
          brew install jq
        fi
        cd packages/engine
        npx --yes react-native-rename@latest "bTeam" -b "gr.benefit.bteam"
        echo $(cat package.json | jq '.name = "engine"' ) > package.json
        rm -rf ios/bTeam/Images.xcassets
        cp -Rf ios/appIconReplacements-bTeam/Images.xcassets ios/bTeam
        cp -f ios/bTeam/GoogleService-Info-bTeam.plist ios/bTeam/GoogleService-Info.plist
        cp -f index.js.bsuite index.js
      workingDirectory: '$(Repo)'

  - task: Bash@3
    displayName: Setup environment variables
    inputs:
      targetType: 'inline'
      script: |
        ENV_WHITELIST=${ENV_WHITELIST:-"^RN_"}
        set | egrep -e $ENV_WHITELIST | sed 's/^RN_//g' | tee .env
        cat .env
        # Copy environment to engine package
        cp .env $(platformPrefix)/
      workingDirectory: '$(Repo)'
    env:
      RN_APP_BUNDLE: bTeam
      RN_API_URL_BTEAM: https://bteam.benmaritime.eu/hermeswebapi
      RN_API_URL_BTEAM_MESSAGES: https://bteam.benmaritime.eu/hermesmessages

  - task: Bash@3
    displayName: Set Xcode version
    inputs:
      targetType: 'inline'
      script: |
        xcodeRoot=/Applications/Xcode_$(Xcode).app
        echo '##vso[task.setvariable variable=MD_APPLE_SDK_ROOT;]'$xcodeRoot;sudo xcode-select --switch $xcodeRoot/Contents/Developer

  - task: InstallAppleCertificate@2
    displayName: Install certificate
    inputs:
      certSecureFile: $(certFile)
      certPwd: '$(iOSCertPass)'
      keychain: 'temp'

  - task: InstallAppleProvisioningProfile@1
    displayName: Install provisioning profile
    inputs:
      provisioningProfileLocation: 'secureFiles'
      provProfileSecureFile: $(provisioningProfileFile)

  # Node.js tool installer v0
  - task: NodeTool@0
    displayName: Setup Node.js
    inputs:
      versionSource: 'spec'
      versionSpec: '18.20.0'

  # Use npm ci for faster, reliable installs when package-lock.json exists
  - task: Npm@1
    displayName: Install dependencies (npm ci)
    condition: ne(variables['npm.cache.restored'], 'true')
    inputs:
      command: 'ci'
      workingDir: '$(Repo)'

  # Skip npm install if cache was restored
  - task: Bash@3
    displayName: Verify npm dependencies
    condition: eq(variables['npm.cache.restored'], 'true')
    inputs:
      targetType: 'inline'
      script: |
        echo "npm cache restored, verifying dependencies..."
        npm list --depth=0 || echo "Some dependencies may need verification"
      workingDirectory: '$(Repo)'

  # Install CocoaPods only if not cached or use system version
  - task: Bash@3
    displayName: Setup CocoaPods
    condition: ne(variables['pods.cache.restored'], 'true')
    inputs:
      targetType: 'inline'
      script: |
        # Use system CocoaPods or install if needed
        if ! gem list cocoapods -i; then
          echo "Installing CocoaPods..."
          sudo gem install cocoapods --no-document
        else
          echo "CocoaPods already available"
        fi

  - task: CocoaPods@0
    displayName: Pod install
    condition: ne(variables['pods.cache.restored'], 'true')
    inputs:
      workingDirectory: '$(Repo)/$(platformPrefix)/ios'
      forceRepoUpdate: false

  - task: Bash@3
    displayName: Verify Pods cache
    condition: eq(variables['pods.cache.restored'], 'true')
    inputs:
      targetType: 'inline'
      script: |
        echo "Pods cache restored, verifying installation..."
        ls -la Pods/ || echo "Pods directory structure may need verification"
      workingDirectory: '$(Repo)/$(platformPrefix)/ios'

  # Optimized Xcode build with parallel processing and caching
  - task: Xcode@5
    displayName: Build project (Manual Signing)
    inputs:
      actions: 'build'
      xcWorkspacePath: '$(Repo)/$(platformPrefix)/ios/$(Workspace).xcworkspace'
      scheme: '$(Scheme)'
      packageApp: true
      archivePath: '$(OutDir)/Archive'
      exportPath: '$(OutDir)'
      signingOption: 'manual'
      signingIdentity: '$(signingIdentity)'
      provisioningProfileName: '$(provisioningProfileName)'
      args: '$(iOSDestinations)'
      useXCPretty: true  # Enable for cleaner output and better performance
    condition: or(eq(variables.autoSigning, 'false'), eq(variables.autoSigning, ''))

  - task: Xcode@5
    displayName: Build project
    inputs:
      actions: 'build'
      xcWorkspacePath: '$(Repo)/$(platformPrefix)/ios/$(Workspace).xcworkspace'
      scheme: '$(Scheme)'
      packageApp: true
      archivePath: '$(OutDir)/Archive'
      exportPath: '$(OutDir)'
      signingOption: 'default'
      args: '$(iOSDestinations)'
      useXCPretty: true
    condition: eq(variables.autoSigning, 'true')

  - task: PublishBuildArtifacts@1
    displayName: Publish artifact
    inputs:
      PathtoPublish: '$(OutDir)'
      ArtifactName: 'iOS_bin'
      publishLocation: 'Container'
